import { makeAutoObservable } from "mobx";
import { Track, EditorElement, TrackType, TimeGap } from "../types";
import { getUid } from "../utils";
import { timeStringToMs } from "../utils/timeUtils";
import { CONSTANTS, TRACK_NAME_TEMPLATES } from "./constants";

// 使用统一的常量定义
const ELEMENT_SPACING_MS = CONSTANTS.ELEMENT_OPERATION.ELEMENT_SPACING_MS;

export class TrackManager {
  private store: any;
  tracks: Track[] = [];
  defaultTracks: Record<TrackType, string> = {
    media: "",
    audio: "",
    text: "",
    caption: "",
  };

  constructor(store: any) {
    this.store = store;
    this.tracks = [];
    makeAutoObservable(this);
  }

  /**
   * 获取元素对应的轨道类型
   * @param elementType 元素类型
   * @returns 轨道类型
   */
  getTrackTypeFromElementType(elementType: string): TrackType {
    if (["image", "video", "shape", "gif"].includes(elementType)) {
      return "media";
    }
    if (elementType === "text") {
      return "text";
    }
    return elementType as TrackType;
  }

  /**
   * 生成轨道名称
   * @param type 轨道类型
   * @returns 轨道名称
   */
  private generateTrackName(type: TrackType): string {
    const template =
      TRACK_NAME_TEMPLATES[type] ||
      `${type.charAt(0).toUpperCase() + type.slice(1)} Track`;
    return `${template} ${this.getTrackCountByType(type) + 1}`;
  }

  /**
   * 根据ID查找轨道
   * @param trackId 轨道ID
   * @returns 轨道对象或undefined
   */
  private findTrackById(trackId: string): Track | undefined {
    return this.tracks.find((t) => t.id === trackId);
  }

  /**
   * 根据ID查找元素
   * @param elementId 元素ID
   * @returns 元素对象或undefined
   */
  private findElementById(elementId: string): EditorElement | undefined {
    return this.store.editorElements.find(
      (el: EditorElement) => el.id === elementId
    );
  }

  /**
   * 初始化轨道，创建默认轨道并为现有元素分配轨道
   */
  initializeTracks() {
    // 清空现有轨道
    this.tracks = [];

    // 创建默认轨道
    this.createDefaultTracks();

    // 为现有元素分配轨道
    this.assignElementsToTracks();
  }

  /**
   * 创建默认轨道（媒体、音频、文本、字幕）
   */
  createDefaultTracks() {
    // const trackTypes: TrackType[] = ["media"];
    // trackTypes.forEach((type) => {
    //   const track = this.createTrack(
    //     type,
    //     `${type.charAt(0).toUpperCase() + type.slice(1)} Track`
    //   );
    //   this.defaultTracks[type] = track.id;
    // });
  }

  /**
   * 将现有元素分配到相应类型的默认轨道
   */
  assignElementsToTracks() {
    // 先处理字幕
    if (this.store.captions && this.store.captions.length > 0) {
      const captionTrackId = this.defaultTracks.caption;
      if (captionTrackId) {
        const captionTrack = this.findTrackById(captionTrackId);
        if (captionTrack) {
          // 字幕不需要添加到轨道的elementIds中，因为它们由CaptionsTrackView单独处理
        }
      }
    }

    // 处理其他元素
    this.store.editorElements.forEach((element: EditorElement) => {
      const trackType = this.getTrackTypeFromElementType(element.type);
      const defaultTrackId = this.defaultTracks[trackType];

      if (defaultTrackId) {
        this.addElementToTrack(defaultTrackId, element.id);
      } else {
        // 如果没有对应类型的默认轨道，创建一个新轨道
        const track = this.createTrack(trackType);
        this.addElementToTrack(track.id, element.id);
      }
    });
  }

  /**
   * 获取指定类型的默认轨道ID
   * @param type 轨道类型
   * @returns 默认轨道ID，如果不存在则返回null
   */
  getDefaultTrackId(type: TrackType): string | null {
    return this.defaultTracks[type] || null;
  }

  /**
   * 设置指定类型的默认轨道
   * @param type 轨道类型
   * @param trackId 轨道ID
   */
  setDefaultTrack(type: TrackType, trackId: string) {
    this.defaultTracks[type] = trackId;
  }

  /**
   * 清除默认轨道设置
   * @param type 轨道类型
   */
  clearDefaultTrack(type: TrackType) {
    this.defaultTracks[type] = "";
  }

  /**
   * 检查轨道是否为默认轨道
   * @param trackId 轨道ID
   * @returns 是否为默认轨道
   */
  isDefaultTrack(trackId: string): boolean {
    return Object.values(this.defaultTracks).includes(trackId);
  }

  /**
   * 创建一个新的轨道
   * @param type 轨道类型
   * @param name 轨道名称（可选）
   * @returns 新创建的轨道
   */
  createTrack(
    type: TrackType,
    name?: string,
    setAsDefault: boolean = true
  ): Track {
    const trackId = getUid();
    const track: Track = {
      id: trackId,
      name: name || this.generateTrackName(type),
      type,
      elementIds: [],
      isVisible: true,
      isLocked: false,
    };
    // 将新轨道添加到数组开头，确保新轨道显示在时间线最上方
    this.tracks.unshift(track);

    // 如果该类型还没有默认轨道，且setAsDefault为true，将新轨道设置为默认轨道
    if (!this.defaultTracks[type] && setAsDefault) {
      this.setDefaultTrack(type, trackId);
      console.log(`设置轨道 "${track.name}" 为 ${type} 类型的默认轨道`);
    }

    return track;
  }

  /**
   * 获取指定类型的轨道数量
   * @param type 轨道类型
   * @returns 轨道数量
   */
  getTrackCountByType(type: TrackType): number {
    return this.tracks.filter((track) => track.type === type).length;
  }

  /**
   * 向轨道添加元素
   * @param trackId 轨道ID
   * @param elementId 元素ID
   */
  addElementToTrack(trackId: string, elementId: string) {
    const track = this.findTrackById(trackId);
    if (!track) return;

    // 确保元素不在其他轨道中
    this.removeElementFromAllTracks(elementId);

    // 添加到指定轨道
    track.elementIds.push(elementId);

    // 更新元素的trackId属性
    const element = this.findElementById(elementId);
    if (element) {
      element.trackId = trackId;
    }
  }

  /**
   * 从所有轨道中移除元素
   * @param elementId 元素ID
   */
  removeElementFromAllTracks(elementId: string) {
    this.tracks.forEach((track) => {
      const index = track.elementIds.indexOf(elementId);
      if (index !== -1) {
        track.elementIds.splice(index, 1);

        // 清除元素的trackId属性
        const element = this.findElementById(elementId);
        if (element) {
          element.trackId = undefined;
        }
      }
    });
  }

  /**
   * 处理删除默认轨道时的元素迁移
   * @param track 要删除的默认轨道
   * @param trackType 轨道类型
   */
  private handleDefaultTrackDeletion(track: Track, trackType: TrackType) {
    // 清除当前的默认轨道设置
    this.clearDefaultTrack(trackType);

    // 寻找同类型的其他轨道作为新的默认轨道
    const sameTypeTrack = this.tracks.find(
      (t) => t.type === trackType && t.id !== track.id
    );

    if (sameTypeTrack) {
      // 设置为新的默认轨道
      this.setDefaultTrack(trackType, sameTypeTrack.id);
      return sameTypeTrack.id;
    } else {
      // 创建新的默认轨道
      const newTrack = this.createTrack(trackType, undefined, true);
      return newTrack.id;
    }
  }

  /**
   * 处理非默认轨道删除时的元素迁移
   * @param trackType 轨道类型
   */
  private handleNonDefaultTrackDeletion(trackType: TrackType): string {
    const defaultTrackId = this.defaultTracks[trackType];

    if (defaultTrackId && this.tracks.some((t) => t.id === defaultTrackId)) {
      return defaultTrackId;
    } else {
      // 如果默认轨道不存在，创建一个
      const newTrack = this.createTrack(trackType, undefined, true);
      return newTrack.id;
    }
  }

  /**
   * 迁移轨道中的元素到目标轨道
   * @param sourceTrack 源轨道
   * @param targetTrackId 目标轨道ID
   * @param isDefaultTrack 是否为默认轨道删除
   */
  private migrateTrackElements(
    sourceTrack: Track,
    targetTrackId: string,
    isDefaultTrack: boolean
  ) {
    console.log(
      `📦 轨道 "${sourceTrack.name}" 中有 ${sourceTrack.elementIds.length} 个元素需要处理`
    );

    sourceTrack.elementIds.forEach((elementId) => {
      const element = this.findElementById(elementId);
      if (element) {
        const trackType = this.getTrackTypeFromElementType(element.type);

        this.addElementToTrack(targetTrackId, elementId);

        const targetTrack = this.findTrackById(targetTrackId);
        const trackName = targetTrack?.name || "未知轨道";

        if (isDefaultTrack) {
          console.log(`📍 元素 ${elementId} 移动到轨道 "${trackName}"`);
        } else {
          console.log(`📍 元素 ${elementId} 移动到默认轨道`);
        }
      }
    });
  }

  /**
   * 删除轨道
   * @param trackId 轨道ID
   * @param forceDelete 是否强制删除（即使是默认轨道）
   */
  deleteTrack(trackId: string, forceDelete: boolean = false) {
    const index = this.tracks.findIndex((t) => t.id === trackId);
    if (index === -1) return;

    const track = this.tracks[index];
    const isDefaultTrack = this.isDefaultTrack(trackId);

    // 如果是默认轨道且不是强制删除，则需要处理元素迁移
    if (isDefaultTrack && !forceDelete) {
      console.log(`⚠️ 尝试删除默认轨道 "${track.name}"，但未启用强制删除模式`);
      return;
    }

    // 处理轨道中的元素
    if (track.elementIds.length > 0) {
      track.elementIds.forEach((elementId) => {
        const element = this.findElementById(elementId);
        if (element) {
          const trackType = this.getTrackTypeFromElementType(element.type);

          let targetTrackId: string;

          if (isDefaultTrack) {
            targetTrackId = this.handleDefaultTrackDeletion(track, trackType);
          } else {
            targetTrackId = this.handleNonDefaultTrackDeletion(trackType);
          }

          this.migrateTrackElements(track, targetTrackId, isDefaultTrack);
        }
      });
    }

    // 如果是默认轨道，清除默认设置
    if (isDefaultTrack) {
      this.clearDefaultTrack(track.type);
      console.log(`🗑️ 清除 ${track.type} 类型的默认轨道设置`);
    }

    // 删除轨道
    this.tracks.splice(index, 1);
    console.log(`🗑️ 已删除轨道 "${track.name}" (${track.type})`);

    // 由于轨道结构发生了变化，需要更新Canvas上的元素显示顺序
    this.store.updateCanvasOrderByTrackOrder();
  }

  /**
   * 获取元素所在的轨道
   * @param elementId 元素ID
   * @returns 元素所在的轨道，如果不在任何轨道中则返回undefined
   */
  getTrackByElementId(elementId: string): Track | undefined {
    return this.tracks.find((track) => track.elementIds.includes(elementId));
  }

  /**
   * 获取轨道中的所有元素
   * @param trackId 轨道ID
   * @returns 轨道中的所有元素
   */
  getElementsByTrackId(trackId: string): EditorElement[] {
    const track = this.findTrackById(trackId);
    if (!track) return [];

    return track.elementIds
      .map((id) => this.findElementById(id))
      .filter(Boolean) as EditorElement[];
  }

  /**
   * 获取所有轨道中的元素，按轨道分组
   * @returns 按轨道分组的元素
   */
  getAllTrackElements(): { track: Track; elements: EditorElement[] }[] {
    return this.tracks.map((track) => ({
      track,
      elements: this.getElementsByTrackId(track.id),
    }));
  }

  /**
   * 获取所有元素，按轨道从上到下，同一轨道从左到右排序
   * @returns 排序后的元素数组
   */
  getAllElementsInDisplayOrder(): EditorElement[] {
    // 获取所有轨道及其元素
    const trackElements = this.getAllTrackElements();

    // 创建结果数组
    const result: EditorElement[] = [];

    // 遍历每个轨道（从下到上）
    for (let i = trackElements.length - 1; i >= 0; i--) {
      const { track, elements } = trackElements[i];

      // 对轨道内的元素按时间排序（从右到左）
      const sortedElements = [...elements].sort(
        (a, b) => b.timeFrame.start - a.timeFrame.start
      );

      // 将排序后的元素添加到结果数组
      result.push(...sortedElements);
    }

    return result;
  }

  /**
   * 获取目标轨道ID（基于选中元素或默认轨道）
   * @param elementType 元素类型
   * @returns 目标轨道ID
   */
  private getTargetTrackId(elementType: string): string {
    const trackType = this.getTrackTypeFromElementType(elementType);

    // 如果有选中的元素
    if (this.store.selectedElement) {
      const selectedElementTrack = this.getTrackByElementId(
        this.store.selectedElement.id
      );
      const selectedElementType = this.getTrackTypeFromElementType(
        this.store.selectedElement.type
      );

      // 如果新元素类型和选中元素类型一样，添加到相同轨道
      if (trackType === selectedElementType && selectedElementTrack) {
        return selectedElementTrack.id;
      } else {
        // 类型不同，创建新轨道（不设为默认轨道，除非该类型还没有默认轨道）
        const hasDefaultTrack = !!this.defaultTracks[trackType];
        const track = this.createTrack(trackType, undefined, !hasDefaultTrack);
        return track.id;
      }
    } else {
      // 没有选中元素，添加到对应类型的默认轨道
      const defaultTrackId = this.defaultTracks[trackType];

      // 检查默认轨道是否仍然存在
      const defaultTrackExists =
        defaultTrackId &&
        this.tracks.some((track) => track.id === defaultTrackId);

      if (defaultTrackExists) {
        return defaultTrackId;
      } else {
        // 如果默认轨道不存在或已被删除，创建一个新轨道并设为默认轨道
        console.log(`${trackType} 类型的默认轨道不存在，创建新的默认轨道`);
        const track = this.createTrack(trackType, undefined, true);
        return track.id;
      }
    }
  }

  /**
   * 当添加新元素时，将其添加到适当的轨道，并进行智能碰撞检测
   * @param element 新添加的元素
   */
  handleNewElement(element: EditorElement) {
    const targetTrackId = this.getTargetTrackId(element.type);

    // 记录原始时间范围
    const originalTimeFrame = {
      start: element.timeFrame.start,
      end: element.timeFrame.end,
    };

    // 智能碰撞检测：检查目标轨道中是否有时间重叠的元素
    // 注意：如果元素从0开始，且与第一个元素重叠，checkElementOverlap会自动平推右侧元素
    const { startTime, endTime, hasOverlap } = this.checkElementOverlap(
      element,
      targetTrackId
    );

    // 如果有碰撞，调整元素的时间范围
    if (hasOverlap) {
      console.log(
        `检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
      );

      // 更新元素的时间范围以避免碰撞
      element.timeFrame = {
        start: startTime,
        end: endTime,
      };

      // 更新最大时间
      this.store.updateMaxTime();
    }

    // 添加元素到目标轨道
    this.addElementToTrack(targetTrackId, element.id);

    // 记录碰撞检测结果供调试
    return {
      targetTrackId,
      hasOverlap,
      originalTime: {
        start: originalTimeFrame.start,
        end: originalTimeFrame.end,
      },
      adjustedTime: {
        start: startTime,
        end: endTime,
      },
    };
  }

  /**
   * 当删除元素时，从轨道中移除
   * @param elementId 被删除的元素ID
   */
  handleElementDeleted(elementId: string) {
    console.log(
      `TrackManager.handleElementDeleted called for id: ${elementId}`
    );
    this.removeElementFromAllTracks(elementId);

    // 删除非默认的空轨道
    this.removeEmptyTracks();
  }

  /**
   * 检查并删除所有空轨道（现在默认包括默认轨道）
   * @param preserveDefaults 是否保护默认轨道不被删除（默认false，即默认删除空的默认轨道）
   */
  removeEmptyTracks(preserveDefaults: boolean = false) {
    console.log("🔍 开始检查空轨道...");

    const allEmptyTracks = this.tracks.filter(
      (track) => track.elementIds.length === 0
    );
    const emptyDefaultTracks = allEmptyTracks.filter((track) =>
      Object.values(this.defaultTracks).includes(track.id)
    );
    const emptyNonDefaultTracks = allEmptyTracks.filter(
      (track) => !Object.values(this.defaultTracks).includes(track.id)
    );

    console.log(
      `📊 空轨道统计: 总计${allEmptyTracks.length}个, 默认轨道${emptyDefaultTracks.length}个, 非默认轨道${emptyNonDefaultTracks.length}个`
    );

    let tracksToRemove = emptyNonDefaultTracks;

    if (!preserveDefaults) {
      // 默认行为：删除所有空轨道，包括默认轨道
      tracksToRemove = [...tracksToRemove, ...emptyDefaultTracks];
      if (emptyDefaultTracks.length > 0) {
        console.log("🗑️ 将删除空的默认轨道（新的默认行为）");
      }
    } else {
      // 保护模式：保留空的默认轨道
      if (emptyDefaultTracks.length > 0) {
        console.log("🛡️ 保护模式：以下空的默认轨道将被保留:");
        emptyDefaultTracks.forEach((track) => {
          console.log(`  - ${track.name} (${track.type}): ${track.id}`);
        });
      }
    }

    if (tracksToRemove.length > 0) {
      console.log("🗑️ 将删除以下空轨道:");
      tracksToRemove.forEach((track) => {
        console.log(`  - ${track.name} (${track.type}): ${track.id}`);
      });

      tracksToRemove.forEach((track) => {
        // 强制删除轨道（包括默认轨道）
        this.deleteTrack(track.id, true);
      });

      // 由于轨道结构发生了变化，需要更新Canvas上的元素显示顺序
      this.store.updateCanvasOrderByTrackOrder();

      console.log(`✅ 成功删除了 ${tracksToRemove.length} 个空轨道`);
    } else {
      console.log("ℹ️ 没有需要删除的空轨道");
    }

    return tracksToRemove.length > 0; // 返回是否删除了轨道
  }

  /**
   * 检查两个元素是否在同一轨道
   * @param elementId1 元素1的ID
   * @param elementId2 元素2的ID
   * @returns 是否在同一轨道
   */
  areElementsInSameTrack(elementId1: string, elementId2: string): boolean {
    const track = this.getTrackByElementId(elementId1);
    return track ? track.elementIds.includes(elementId2) : false;
  }

  /**
   * 检查元素在时间线上是否重叠
   * @param element1 元素1
   * @param element2 元素2
   * @returns 是否重叠
   */
  areElementsOverlapping(
    element1: EditorElement,
    element2: EditorElement
  ): boolean {
    return (
      (element1.timeFrame.start <= element2.timeFrame.start &&
        element2.timeFrame.start < element1.timeFrame.end) ||
      (element2.timeFrame.start <= element1.timeFrame.start &&
        element1.timeFrame.start < element2.timeFrame.end)
    );
  }

  /**
   * 获取轨道中的元素，按时间排序
   * @param trackId 轨道ID
   * @returns 按时间排序的元素
   */
  getSortedElementsByTrackId(trackId: string): EditorElement[] {
    const elements = this.getElementsByTrackId(trackId);
    return elements.sort((a, b) => a.timeFrame.start - b.timeFrame.start);
  }

  /**
   * 检查元素在轨道中是否与其他元素重叠，并提供调整建议
   * @param element 要检查的元素
   * @param trackId 轨道ID
   * @returns 调整后的时间范围和是否有重叠
   */
  checkElementOverlap(
    element: EditorElement,
    trackId: string
  ): { startTime: number; endTime: number; hasOverlap: boolean } {
    const track = this.findTrackById(trackId);
    if (!track) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    const trackElements = this.getSortedElementsByTrackId(trackId).filter(
      (el) => el.id !== element.id
    );

    if (trackElements.length === 0) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    let adjustedStartTime = element.timeFrame.start;
    let adjustedEndTime = element.timeFrame.end;
    let hasOverlap = false;
    const duration = element.timeFrame.end - element.timeFrame.start;

    // 找出所有可能重叠的元素
    const overlappingElements = trackElements.filter((otherElement) => {
      return this.areElementsOverlapping(element, otherElement);
    });

    // 特殊情况处理：如果元素从0开始
    if (element.timeFrame.start === 0) {
      // 获取轨道中的第一个元素（按开始时间排序）
      const firstElement = trackElements.length > 0 ? trackElements[0] : null;

      if (firstElement) {
        // 检查从0开始到第一个元素的间隔是否小于新元素的duration
        const availableSpace = firstElement.timeFrame.start;

        if (availableSpace < duration) {
          console.log(
            `检测到从0开始的元素需要 ${duration}ms 空间，但只有 ${availableSpace}ms 可用，将向右平推所有元素`
          );

          // 计算需要平移的距离
          const pushDistance = duration - availableSpace;

          // 向右平推所有元素
          this.pushElementsRight(trackId, pushDistance);

          // 返回原始时间范围，因为我们已经平推了其他元素
          return {
            startTime: element.timeFrame.start,
            endTime: element.timeFrame.end,
            hasOverlap: false, // 设置为false，因为我们已经解决了重叠问题
          };
        }
      }
    }

    if (overlappingElements.length > 0) {
      hasOverlap = true;

      // 按照与当前元素的距离排序
      const sortedOverlappingElements = overlappingElements.sort((a, b) => {
        const distanceA = Math.min(
          Math.abs(element.timeFrame.start - a.timeFrame.end),
          Math.abs(element.timeFrame.end - a.timeFrame.start)
        );
        const distanceB = Math.min(
          Math.abs(element.timeFrame.start - b.timeFrame.end),
          Math.abs(element.timeFrame.end - b.timeFrame.start)
        );
        return distanceA - distanceB;
      });

      // 获取最近的重叠元素
      const nearest = sortedOverlappingElements[0];

      // 计算调整方向
      const distanceToEnd = Math.abs(
        element.timeFrame.start - nearest.timeFrame.end
      );
      const distanceToStart = Math.abs(
        element.timeFrame.end - nearest.timeFrame.start
      );

      if (distanceToEnd <= distanceToStart) {
        // 将元素移动到重叠元素的后面
        adjustedStartTime = nearest.timeFrame.end;
        adjustedEndTime = adjustedStartTime + duration;
      } else {
        // 将元素移动到重叠元素的前面
        adjustedEndTime = nearest.timeFrame.start;
        adjustedStartTime = adjustedEndTime - duration;
      }

      // 确保调整后的时间不为负
      if (adjustedStartTime < 0) {
        adjustedStartTime = 0;
        adjustedEndTime = duration;
      }
    }

    return {
      startTime: adjustedStartTime,
      endTime: adjustedEndTime,
      hasOverlap,
    };
  }

  /**
   * 向右平推轨道中的所有元素
   * @param trackId 轨道ID
   * @param distance 平推距离（毫秒）
   */
  pushElementsRight(trackId: string, distance: number) {
    // 获取轨道中的所有元素，按开始时间排序
    const elements = this.getSortedElementsByTrackId(trackId);

    if (elements.length === 0) return;

    console.log(
      `向右平推轨道 ${trackId} 中的 ${elements.length} 个元素，距离 ${distance}ms`
    );

    // 从左到右依次平推每个元素
    for (const element of elements) {
      const newStartTime = element.timeFrame.start + distance;
      const newEndTime = element.timeFrame.end + distance;

      console.log(
        `平推元素 ${element.id}，从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${newStartTime}-${newEndTime}`
      );

      // 更新元素的时间范围，并标记为拖拽结束以触发maxDuration更新
      this.store.updateEditorElementTimeFrame(
        element,
        {
          start: newStartTime,
          end: newEndTime,
        },
        true
      );
    }
  }

  /**
   * 检测轨道中元素之间的时间间隙
   * @param trackId 轨道ID
   * @returns 间隙数组
   */
  detectTimeGaps(trackId: string): TimeGap[] {
    const elements = this.getSortedElementsByTrackId(trackId);
    const gaps: TimeGap[] = [];

    if (elements.length === 0) return gaps;

    // 检查第一个元素之前是否有间隙（从时间0开始）
    const firstElement = elements[0];
    if (firstElement.timeFrame.start > 0) {
      gaps.push({
        id: `gap-start-${trackId}`,
        startTime: 0,
        endTime: firstElement.timeFrame.start,
        duration: firstElement.timeFrame.start,
        trackId,
        afterElementId: firstElement.id,
      });
    }

    // 检查相邻元素之间的间隙
    for (let i = 0; i < elements.length - 1; i++) {
      const currentElement = elements[i];
      const nextElement = elements[i + 1];

      if (currentElement.timeFrame.end < nextElement.timeFrame.start) {
        const gapDuration =
          nextElement.timeFrame.start - currentElement.timeFrame.end;
        gaps.push({
          id: `gap-${currentElement.id}-${nextElement.id}`,
          startTime: currentElement.timeFrame.end,
          endTime: nextElement.timeFrame.start,
          duration: gapDuration,
          trackId,
          beforeElementId: currentElement.id,
          afterElementId: nextElement.id,
        });
      }
    }

    return gaps;
  }

  /**
   * 删除时间间隙，将后续元素向前移动
   * @param gap 要删除的间隙
   */
  deleteTimeGap(gap: TimeGap) {
    console.log(`删除间隙: ${gap.id}, 时长: ${gap.duration}ms`);

    // 开始历史分组操作
    this.store.startHistoryGroup("删除间隙");

    try {
      const elements = this.getSortedElementsByTrackId(gap.trackId);

      // 找到需要移动的元素（间隙结束时间之后的所有元素）
      const elementsToMove = elements.filter(
        (element) => element.timeFrame.start >= gap.endTime
      );

      console.log(`需要向前移动 ${elementsToMove.length} 个元素`);

      // 向前移动所有后续元素
      for (const element of elementsToMove) {
        const originalDuration =
          element.timeFrame.end - element.timeFrame.start;
        const newStartTime = element.timeFrame.start - gap.duration;
        const newEndTime = newStartTime + originalDuration; // 保持原始duration不变

        console.log(
          `移动元素 ${element.id}，从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${newStartTime}-${newEndTime}，duration保持: ${originalDuration}ms`
        );

        // 确保不会移动到负时间
        const finalStartTime = Math.max(0, newStartTime);
        const finalEndTime = finalStartTime + originalDuration; // 基于调整后的开始时间重新计算结束时间

        // 更新元素的时间范围，保持duration不变
        this.store.updateEditorElementTimeFrame(
          element,
          {
            start: finalStartTime,
            end: finalEndTime,
          },
          true
        );
      }

      // 更新最大时间
      this.store.updateMaxTime();

      // 保存更改
      this.store.saveChange("删除间隙");
    } finally {
      // 结束历史分组操作
      setTimeout(() => {
        this.store.endHistoryGroup();
      }, 100);
    }
  }

  /**
   * 修复轨道中所有元素的重叠问题
   * @param trackId 轨道ID
   */
  fixTrackElementsOverlap(trackId: string) {
    const elements = this.getSortedElementsByTrackId(trackId);

    if (elements.length <= 1) return;

    // 逐个检查并修复重叠
    for (let i = 1; i < elements.length; i++) {
      const currentElement = elements[i];
      const previousElement = elements[i - 1];

      // 检查是否有重叠
      if (this.areElementsOverlapping(currentElement, previousElement)) {
        // 计算当前元素的持续时间
        const currentDuration =
          currentElement.timeFrame.end - currentElement.timeFrame.start;

        // 调整当前元素的开始时间
        const newStartTime = previousElement.timeFrame.end + ELEMENT_SPACING_MS;
        const newEndTime = newStartTime + currentDuration;

        // 更新元素的时间范围，并标记为拖拽结束以触发maxDuration更新
        this.store.updateEditorElementTimeFrame(
          currentElement,
          {
            start: newStartTime,
            end: newEndTime,
          },
          true
        );

        // 递归检查后续元素，确保所有重叠都被修复
        this.fixTrackElementsOverlap(trackId);
        return; // 提前返回，避免重复处理
      }
    }
  }

  /**
   * 在指定位置创建新轨道
   * @param type 轨道类型
   * @param position 位置索引，如果为-1则添加到末尾
   * @param name 轨道名称（可选）
   * @returns 新创建的轨道
   */
  createTrackAtPosition(
    type: TrackType,
    position: number = -1,
    name?: string
  ): Track {
    const track = this.createTrack(type, name);

    // 如果指定了位置，则将轨道移动到该位置
    if (position >= 0 && position < this.tracks.length) {
      // 由于createTrack已经将轨道添加到开头，我们需要先移除它
      const tracks = [...this.tracks];
      const newTrack = tracks.shift()!; // 移除第一个添加的轨道

      // 在指定位置插入轨道
      tracks.splice(position, 0, newTrack);
      this.tracks = tracks;

      // 由于轨道顺序发生了变化，需要更新Canvas上的元素显示顺序
      this.store.updateCanvasOrderByTrackOrder();
    }

    return track;
  }

  /**
   * 检查元素类型是否与轨道类型匹配
   * @param elementType 元素类型
   * @param trackType 轨道类型
   * @returns 是否匹配
   */
  private isElementTypeMatchingTrackType(
    elementType: string,
    trackType: TrackType
  ): boolean {
    // 对于image、video、shape、gif类型的元素，它们可以放在media类型的轨道中
    if (
      ["image", "video", "shape", "gif"].includes(elementType) &&
      trackType === "media"
    ) {
      return true;
    }
    // text元素只能放在text轨道中
    if (elementType === "text" && trackType === "text") {
      return true;
    }
    // audio元素只能放在audio轨道中
    if (elementType === "audio" && trackType === "audio") {
      return true;
    }
    return false;
  }

  /**
   * 将元素从一个轨道移动到另一个轨道
   * @param elementId 要移动的元素ID
   * @param targetTrackId 目标轨道ID
   * @returns 是否成功移动
   */
  moveElementToTrack(elementId: string, targetTrackId: string): boolean {
    // 获取元素
    const element = this.findElementById(elementId);
    if (!element) return false;

    // 获取目标轨道
    const targetTrack = this.findTrackById(targetTrackId);
    if (!targetTrack) return false;

    // 检查元素类型是否与轨道类型匹配
    // 注意：我们允许不同类型的元素放在同一轨道，但会在UI上给出提示
    const isTypeMatch = this.isElementTypeMatchingTrackType(
      element.type,
      targetTrack.type
    );

    // 将元素添加到目标轨道
    this.addElementToTrack(targetTrackId, elementId);

    // 检查并修复目标轨道中的元素重叠
    this.fixTrackElementsOverlap(targetTrackId);

    // 根据轨道顺序更新Canvas上的元素显示顺序
    this.store.updateCanvasOrderByTrackOrder();

    // 选中移动后的元素
    this.store.setSelectedElement(element);

    return true;
  }

  /**
   * 清理轨道中无效的元素ID（不存在于editorElements数组中的ID）
   * @returns 被清理的元素ID数量
   */
  cleanupInvalidElementIds(): number {
    let removedCount = 0;

    // 遍历所有轨道
    this.tracks.forEach((track) => {
      // 找出轨道中存在但在editorElements中不存在的元素ID
      const invalidElementIds = track.elementIds.filter(
        (elementId) => !this.findElementById(elementId)
      );

      // 如果有无效的元素ID
      if (invalidElementIds.length > 0) {
        // 记录日志
        console.log(
          `从轨道 "${track.name}" 中移除 ${invalidElementIds.length} 个无效的元素ID`
        );

        // 从轨道中移除这些ID
        track.elementIds = track.elementIds.filter(
          (elementId) => !invalidElementIds.includes(elementId)
        );

        // 更新计数
        removedCount += invalidElementIds.length;
      }
    });

    // 如果有元素被移除，可能需要删除空轨道
    if (removedCount > 0) {
      this.removeEmptyTracks();
    }

    return removedCount;
  }
}
